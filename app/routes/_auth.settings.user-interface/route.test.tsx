import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { redirect, data } from "react-router";

import Route, { loader } from "./route";
import { renderRouter } from "~/utils/testUtils";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import { getTheme, saveUiSettingsInCookies } from "~/utils/uiCookies";

// Mock the utility functions
vi.mock("~/utils/flagsInCookies", () => ({
  isFlagEnabled: vi.fn(),
}));

vi.mock("~/utils/uiCookies", () => ({
  getTheme: vi.fn(),
  saveUiSettingsInCookies: vi.fn(),
}));

// Mock window.location.reload
const mockReload = vi.fn();
Object.defineProperty(window, "location", {
  value: {
    reload: mockReload,
  },
  writable: true,
});

// Mock window.alert
const mockAlert = vi.fn();
vi.stubGlobal("alert", mockAlert);

describe("loader", () => {
  it("redirects to /settings when EnableUISettings flag is disabled", async () => {
    vi.mocked(isFlagEnabled).mockReturnValue(false);

    const request = new Request("http://localhost/settings/user-interface");

    const result = await loader({ request, params: {}, context: {} });

    expect(isFlagEnabled).toHaveBeenCalledWith(request, "EnableUISettings");
    expect(result).toEqual(redirect("/settings"));
  });

  it("returns theme data when EnableUISettings flag is enabled", async () => {
    vi.mocked(isFlagEnabled).mockReturnValue(true);
    vi.mocked(getTheme).mockReturnValue("dark");

    const request = new Request("http://localhost/settings/user-interface", {
      headers: { Cookie: "ui-settings=%7B%22theme%22%3A%22dark%22%7D" },
    });

    const result = await loader({ request, params: {}, context: {} });

    expect(isFlagEnabled).toHaveBeenCalledWith(request, "EnableUISettings");
    expect(getTheme).toHaveBeenCalledWith(
      "ui-settings=%7B%22theme%22%3A%22dark%22%7D"
    );
    expect(result).toEqual({ theme: "dark" });
  });

  it("handles null cookie header", async () => {
    vi.mocked(isFlagEnabled).mockReturnValue(true);
    vi.mocked(getTheme).mockReturnValue("light");

    const request = new Request("http://localhost/settings/user-interface");

    const result = await loader({ request, params: {}, context: {} });

    expect(getTheme).toHaveBeenCalledWith(null);
    expect(result).toEqual({ theme: "light" });
  });
});

describe("Component", () => {
  const renderUserInterfaceRoute = (theme = "light") => {
    const routes = [
      {
        path: "/settings/user-interface",
        Component: Route,
        loader: () => data({ theme }),
        hydrateFallbackElement: <div>Loading...</div>,
      },
    ];

    return renderRouter(routes, ["/settings/user-interface"]);
  };

  it("renders all required UI elements", async () => {
    renderUserInterfaceRoute();

    await screen.findByText("User Interface");

    // Check for main heading
    expect(
      screen.getByRole("heading", { name: "User Interface" })
    ).toBeInTheDocument();

    // Check for note section
    expect(screen.getByRole("heading", { name: "Note" })).toBeInTheDocument();

    // Check for theme label
    expect(screen.getByText("Theme")).toBeInTheDocument();

    // Check for dropdown with Light selected
    const dropdown = screen.getByRole("combobox");
    expect(dropdown).toHaveTextContent("Light");
    expect(dropdown).toBeInTheDocument();

    // Check for save button
    expect(
      screen.getByRole("button", { name: /save and refresh/i })
    ).toBeInTheDocument();

    // Check for back button (mobile)
    const buttons = screen.getAllByRole("button");
    const backButton = buttons.find((button) =>
      button.querySelector('a[href="/settings"]')
    );
    expect(backButton).toBeInTheDocument();
    expect(backButton).toHaveClass("md:hidden");
  });

  it("renders with dark theme when specified", async () => {
    renderUserInterfaceRoute("dark");

    await screen.findByText("User Interface");

    const dropdown = screen.getByRole("combobox");
    expect(dropdown).toHaveTextContent("Dark");
  });
});
