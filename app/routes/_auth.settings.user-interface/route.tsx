import { useState } from "react";
import {
  Link,
  LoaderFunctionArgs,
  redirect,
  useLoaderData,
} from "react-router";
import { ArrowLeft, Save } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import SettingTypeTextBlock from "~/routes/_auth.settings.$/components/SettingTypeTextBlock";
import SettingTypeDropdown from "~/routes/_auth.settings.$/components/SettingTypeDropdown";
import SettingTypeSwitch from "../_auth.settings.$/components/SettingTypeSwitch";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import {
  getUiSettingsFromCookies,
  saveUiSettingsInCookies,
} from "~/utils/uiCookies";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // redirect to /settings if flag is not enabled
  if (!isFlagEnabled(request, "EnableUISettings")) {
    return redirect("/settings");
  }

  return {
    uiSettings: getUiSettingsFromCookies(request.headers.get("Cookie")),
  };
};

const UserInterface = () => {
  const { uiSettings: uiSettingsData } = useLoaderData();

  const [uiSettings, setUiSettings] = useState<{ [key: string]: any }>(
    uiSettingsData
  );
  const [showSavedMsg, setShowSavedMsg] = useState(false);

  const updateSettingsObject = (id: string, value: any) => {
    setUiSettings((prevSettings) => ({
      ...prevSettings,
      [id]: value,
    }));
  };

  const handleSubmit = () => {
    saveUiSettingsInCookies(uiSettings);

    setShowSavedMsg(true);

    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  // eslint-disable-next-line no-console
  console.log("uiSettings", uiSettings);

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings">
            <ArrowLeft />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">User Interface</h2>
      </div>

      <SettingTypeTextBlock
        id="note"
        label="Note"
        details={
          'All settings in this section are "saved locally", and are applied only to this browser on this device.'
        }
      />

      <SettingTypeDropdown
        id="theme"
        label="Theme"
        value={uiSettings.theme}
        options={[
          { id: "light", label: "Light" },
          { id: "dark", label: "Dark" },
        ]}
        onChange={updateSettingsObject}
      />

      <SettingTypeSwitch
        id="navbar"
        label="Show new nav bar"
        value={uiSettings.navbar}
        onChange={updateSettingsObject}
      />

      <div className="flex items-center">
        <Button onClick={handleSubmit}>
          <Save />
          Save and refresh
        </Button>
        {showSavedMsg && (
          <span className="ml-4 text-sm text-success">
            Changes saved! Refreshing your page..
          </span>
        )}
      </div>
    </div>
  );
};

export default UserInterface;
