import { format } from "date-fns";
import {
  CircleCheckBig,
  Clock,
  Lightbulb,
  NotebookPen,
  ReceiptText,
  Tag,
  Text,
  Users,
} from "lucide-react";

import { NoteResponse, CRMSyncSection } from "~/api/openapi/generated";
import { secondsToDurationString } from "~/utils/time";

export const getCardData = (data?: NoteResponse) =>
  data && [
    {
      Icon: Text,
      name: "Meeting title",
      key: "title",
      selectionContent: data.meetingName,
      isReadOnly: true,
    },
    {
      Icon: Clock,
      name: "Meeting details",
      key: CRMSyncSection.MeetingDetails,
      selectionContent: (
        <ul className="list-inside list-disc">
          <li>
            Created: {format(new Date(data.created), "ccc, MMM do, h:mm aaa")}
          </li>
          <li>
            Duration: {secondsToDurationString(data.meetingDurationSeconds)}
          </li>
        </ul>
      ),
    },
    {
      Icon: Users,
      name: "Meeting attendees",
      key: CRMSyncSection.Attendees,
      selectionContent: data.attendees
        ?.map((attendee) => attendee.name)
        .join(", "),
    },
    {
      Icon: Tag,
      name: "Keywords",
      key: CRMSyncSection.Keywords,
      selectionContent: data.tags?.join(", "),
    },
    {
      Icon: CircleCheckBig,
      name: "Action Items",
      key: CRMSyncSection.Tasks,
      selectionContent: (
        <>
          <ul className="list-inside list-disc">
            {data.actionItems?.map((item) => (
              <li key={item.uuid}>{item.content}</li>
            ))}
          </ul>
        </>
      ),
    },
    {
      Icon: Lightbulb,
      name: "Key Takeaways",
      key: CRMSyncSection.KeyTakeaways,
      selectionContent: (
        <>
          <ul className="list-inside list-disc">
            {data.keyTakeaways?.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </>
      ),
    },
    {
      Icon: NotebookPen,
      name: "Advisor Notes",
      key: CRMSyncSection.AdvisorNotes,
      selectionContent: (
        <>
          <ul className="list-inside list-disc">
            {data.advisorNotes?.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </>
      ),
    },
    {
      Icon: ReceiptText,
      name: "Summary",
      key: CRMSyncSection.Summary,
      selectionContent: (
        <>
          <ul className="list-inside list-disc">
            {data.summaryByTopics?.sections?.map(
              ({ topic, bullets }, index) => {
                return (
                  <li key={index}>
                    {topic}
                    <ul className="ml-3 list-inside list-[circle]">
                      {bullets.map((bullet, index) => (
                        <li key={index} className="">
                          {bullet}
                        </li>
                      ))}
                    </ul>
                  </li>
                );
              }
            )}
          </ul>
        </>
      ),
    },
  ];
