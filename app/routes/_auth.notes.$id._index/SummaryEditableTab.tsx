import { useState, useEffect, Dispatch } from "react";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Typography } from "~/@ui/Typography";
import {
  EditableNoteActions,
  EditableNoteState,
} from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import { Button } from "~/@shadcn/ui/button";
import { copyToClipboard } from "~/utils/copyToClipboard";
import { cn } from "~/@shadcn/utils";
import ReactMarkdown from "react-markdown";
import { useFlag } from "~/context/flags";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { CopyIcon, Maximize2, Plus, WandSparkles } from "lucide-react";

type SummaryEditableTabProps = {
  dispatch: Dispatch<EditableNoteActions>;
  summaryByTopics: EditableNoteState["summaryByTopics"];
  disabled: boolean;
  onEdit: () => void;
  activeIndex?: number | null;
  isVisible: boolean;
  startSearch: (searchQuery: string) => void;
  EnableQuickQuestions?: boolean;
  isEditMode?: boolean;
};

export const SummaryEditableTab = ({
  dispatch,
  summaryByTopics,
  disabled,
  onEdit,
  activeIndex,
  isVisible,
  startSearch,
  isEditMode,
}: SummaryEditableTabProps) => {
  const [topicsState, setTopicsState] = useState(
    summaryByTopics.map((topic) => ({
      ...topic,
      value: topic.value,
      nodes: topic.nodes.map((node) => ({ ...node, value: node.value })),
    }))
  );
  const EnableQuickQuestions = useFlag("EnableQuickQuestions");
  useEffect(() => {
    setTopicsState(
      summaryByTopics.map((topic) => ({
        ...topic,
        value: topic.value,
        nodes: topic.nodes.map((node) => ({ ...node, value: node.value })),
      }))
    );
  }, [summaryByTopics]);

  const handleAddSection = () => {
    if (disabled) return;
    dispatch({ type: "addSummarySection" });
  };

  const handleCopySection = () => {
    const text = [
      "Meeting summary",
      ...topicsState.map(
        (topic, index) =>
          `${index + 1}. ${topic.value}\n${topic.nodes
            .map((node) => `   • ${node.value}`)
            .join("\n")}`
      ),
    ].join("\n");
    copyToClipboard(text, "Meeting summary");
  };

  const handleNodeKeyDown = (
    event: React.KeyboardEvent<HTMLTextAreaElement>,
    parentUuid: string
  ) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      dispatch({ type: "addSummaryNodeToParentSection", parentUuid });
    }
  };

  const handleTopicChange = (index: number, value: string) => {
    const updatedTopics = [...topicsState];
    updatedTopics[index]!.value = value;
    setTopicsState(updatedTopics);
    dispatch({
      type: "updateSummarySection",
      uuid: updatedTopics[index]!.uuid,
      nextValue: value,
    });
    onEdit();
  };

  const handleNodeChange = (
    topicIndex: number,
    nodeIndex: number,
    value: string
  ) => {
    const updatedTopics = [...topicsState];
    updatedTopics[topicIndex]!.nodes[nodeIndex]!.value = value;
    setTopicsState(updatedTopics);
    dispatch({
      type: "updateSummaryNodeInParentSection",
      parentUuid: updatedTopics[topicIndex]!.uuid,
      uuid: updatedTopics[topicIndex]!.nodes[nodeIndex]!.uuid,
      nextValue: value,
    });
    onEdit();
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Typography variant="h3" color="primary">
          Meeting summary
        </Typography>
        <CopyIcon
          className="ml-2 cursor-pointer text-gray-500 hover:text-black"
          onClick={handleCopySection}
        />
        {!disabled && (
          <Plus
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleAddSection}
          />
        )}
      </div>
      <ol className="flex list-decimal flex-col gap-2 pl-4">
        {topicsState.map((topic, index) => (
          <li
            key={topic.uuid}
            className={cn(
              "whitespace-pre-wrap rounded-md p-3 text-warning",
              index === activeIndex ? "bg-[#FFFCEF] shadow-md" : ""
            )}
          >
            <div className="flex items-center gap-2">
              {disabled ? (
                <Typography
                  className={`overflow-hidden break-words text-xl font-semibold leading-9 ${
                    index === activeIndex ? "bg-[#FFFCEF]" : ""
                  }`}
                  asChild
                >
                  <ReactMarkdown>{topic.value}</ReactMarkdown>
                </Typography>
              ) : (
                <TextareaGrowable
                  className={`border-none text-xl font-semibold leading-9 !opacity-100 focus:outline-none ${
                    index === activeIndex ? "bg-[#FFFCEF]" : ""
                  }`}
                  autoFocus={topic.autoFocus}
                  value={topic.value}
                  placeholder="Add a summary topic"
                  isVisible={isVisible}
                  disabled={disabled}
                  onChange={(event) =>
                    handleTopicChange(index, event.currentTarget.value)
                  }
                  onBlur={(event) => {
                    if (
                      !disabled &&
                      event.currentTarget.value.trim().length === 0
                    ) {
                      dispatch({
                        type: "removeSummarySection",
                        uuid: topic.uuid,
                      });
                      onEdit();
                    }
                  }}
                />
              )}

              {/* This section is disabled in edit mode */}
              {EnableQuickQuestions && !isEditMode && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"outline"}
                      onClick={() => {
                        startSearch(
                          "Give me a more detailed version of the topic: " +
                            topic.value
                        );
                      }}
                    >
                      <Maximize2 />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    Get more details for this section
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <ul className="flex list-disc flex-col gap-1 border-none pl-4">
              {topic.nodes.map((node, nodeIndex) => (
                <li
                  key={node.uuid}
                  className="whitespace-pre-wrap text-warning"
                >
                  <div className="flex items-center gap-2">
                    {disabled ? (
                      <Typography
                        className={`overflow-hidden break-words text-lg ${
                          index === activeIndex ? "bg-[#FFFCEF]" : ""
                        }`}
                        asChild
                      >
                        <ReactMarkdown>{node.value}</ReactMarkdown>
                      </Typography>
                    ) : (
                      <TextareaGrowable
                        className={`border-none text-lg !opacity-100 focus:outline-none ${
                          index === activeIndex ? "bg-[#FFFCEF]" : ""
                        }`}
                        autoFocus={node.autoFocus}
                        value={node.value}
                        placeholder="Add a summary item"
                        disabled={disabled}
                        isVisible={isVisible}
                        onChange={(event) =>
                          handleNodeChange(
                            index,
                            nodeIndex,
                            event.currentTarget.value
                          )
                        }
                        onBlur={(event) => {
                          if (
                            !disabled &&
                            event.currentTarget.value.trim().length === 0
                          ) {
                            dispatch({
                              type: "removeSummaryNodeFromParentSection",
                              parentUuid: topic.uuid,
                              uuid: node.uuid,
                            });
                            onEdit();
                          }
                        }}
                        onKeyDown={(event) =>
                          handleNodeKeyDown(event, topic.uuid)
                        }
                      />
                    )}
                    {EnableQuickQuestions && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={"ghost"}
                            onClick={() => {
                              startSearch(
                                "Expand on the following comment, including all relevant details: " +
                                  node.value
                              );
                            }}
                          >
                            <WandSparkles />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          Convert into its own section
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </li>
              ))}
              {!disabled && (
                <Button
                  className="-ml-4 self-start"
                  variant="ghost"
                  onClick={(event) => {
                    event.preventDefault();
                    dispatch({
                      type: "addSummaryNodeToParentSection",
                      parentUuid: topic.uuid,
                    });
                  }}
                >
                  <Plus className="!h-5 !w-5" />
                  <span className="grow">Add item</span>
                </Button>
              )}
            </ul>
          </li>
        ))}
      </ol>
    </>
  );
};
