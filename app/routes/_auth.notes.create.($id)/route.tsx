import { use<PERSON><PERSON>back, useContext, useEffect, useRef, useState } from "react";
import { useEventSource } from "remix-utils/sse/react";
import {
  LoaderFunctionArgs,
  data,
  redirect,
  type ActionFunctionArgs,
  type MetaFunction,
} from "react-router";
import { SerializeFrom } from "~/types/remix";
import {
  unstable_usePrompt,
  useActionData,
  useBeforeUnload,
  useFetcher,
  useLoaderData,
  useLocation,
  useNavigate,
  useNavigation,
  useParams,
  useSearchParams,
  useSubmit,
} from "react-router";
import { Steps } from "intro.js-react";

import { logError } from "~/utils/log.server";
import {
  RecorderCard,
  RecorderContext,
  RecorderProvider,
} from "~/routes/_auth.notes.create.($id)/Recorder";
import { AudioFileUploader } from "~/routes/_auth.notes.create.($id)/AudioFileUploader";
import { BackButton } from "~/@ui/buttons/BackButton";
import { SaveButton } from "~/@ui/buttons/SaveButton";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
} from "~/@shadcn/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { Calendar, Clock, Users } from "lucide-react";
import { AfterHydration } from "~/utils/hydration";
import { Typography } from "~/@ui/Typography";
import { addHours, format } from "date-fns";
import { getPayloadFromForm } from "~/routes/_auth.notes.create.($id)/utils";
import getMeetingNameFromAttendees from "./utils/getMeetingNameFromAttendees";
import tutorialSteps from "./utils/tutorialSteps";
import { getNoteById } from "~/api/notes/getNoteById.server";
import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";
import { NoteTakerController } from "~/routes/_auth.notes.create.($id)/NoteTakerController";
import { ActionTypes } from "~/utils/const";
import { isValidTitle } from "~/utils/validation";
import { getAttendeeOptions } from "~/api/attendees/getAttendeeOptions.server";
import { AttendeeOptions, AttendeeOptionsStruct } from "~/api/attendees/types";
import { AttendeesMultiSelect } from "~/@ui/attendees/AttendeeMultiSelect";
import { Id, toast } from "react-toastify";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { getEventStreamParams } from "~/api/bots/events.server";
import { cn } from "~/@shadcn/utils";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  Bot,
  BotApi,
  BotMeetingType,
  BotStatus,
  ClientInteraction,
  Configuration,
  MeetingArtifactsApi,
  MeetingCategory,
  MeetingType,
  MeetingTypesResponse,
  NoteApi,
  NoteResponse,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { resolvedAttendeeOptions } from "~/utils/attendeeOptions";
import { ConfirmModal } from "~/@ui/ConfirmModal";
import { DeleteButton } from "~/@ui/buttons/DeleteButton";
import isValidPhoneNumber from "libphonenumber-js";
import "react-phone-number-input/style.css";
import { useFlag } from "~/context/flags";
import { MeetingTypeSelector } from "./components/MeetingTypeSelector";
import { ToggleGroup, ToggleGroupItem } from "~/@shadcn/ui/toggle-group";
import MeetingPrepTab from "./components/MeetingPrepTab";
import MeetingTypeModal from "./components/MeetingTypeModal";
import safeAtob from "~/utils/safeAtob";
import useOnboarding from "~/utils/useOnboarding";
import { Separator } from "~/@shadcn/ui/separator";

// Exports
export const meta: MetaFunction = () => [
  { title: "Create note" },
  { name: "description", content: "Create new note" },
];

const fragmentDurationSeconds = 10;

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const noteID = new URL(request.url).searchParams.get("noteID");
    const createFormData = await getPayloadFromForm(request);

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteAPI = new NoteApi(configuration);

    const action = createFormData.action ?? ActionTypes.SAVE_NOTE;

    switch (action) {
      case ActionTypes.RESUME_RECORDING:
        if (!createFormData.botId) {
          throw Error("Bot action requested without a bot ID");
        }
        await new BotApi(configuration).botResumeRecording({
          botUuid: createFormData.botId,
        });
        return null;
      case ActionTypes.PAUSE_RECORDING:
        if (!createFormData.botId) {
          throw Error("Bot action requested without a bot ID");
        }
        await new BotApi(configuration).botPauseRecording({
          botUuid: createFormData.botId,
        });
        return null;
      case ActionTypes.SAVE_NOTE:
      case ActionTypes.CREATE_DRAFT_NOTE:
      case ActionTypes.SAVE_NOTE_AND_START_MEETING:
      case ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING:
        // Create or update the note.
        //
        // If there is a meeting link and the scheduled notes feature is not enabled, this will also
        // create a bot and send it to the meeting.
        const res = await noteAPI.noteCreateOrUpdateNote({
          noteId: noteID,
          meetingType: createFormData.meetingType,
          meetingName: createFormData.meetingName,
          attendees: createFormData.attendees,
          meetingLink: createFormData.meetingLink,
          meetingSourceId: createFormData.meetingSourceID,
          scheduledStartTime: createFormData.startTime,
          scheduledEventUuid: createFormData.scheduledEventUUID,
          scheduledEndTime: createFormData.endTime,
          audioData: createFormData.fileContent,
          fileType: createFormData.fileType,
          clientId: createFormData.linkedCRMEntityID,
          clientName: createFormData.linkedCRMEntityName,
        });

        // If the note is completed, redirect to the notes list.
        if (res.completed) {
          return redirect(`/notes/`);
        }

        // If we are creating a draft note, do not redirect, but return the ID of the draft note in
        // the action response.
        if (action === ActionTypes.CREATE_DRAFT_NOTE) {
          return data({ draftNoteID: res.noteId });
        }

        // Trigger the requested bot action (if there is one).
        if (action === ActionTypes.SAVE_NOTE_AND_START_MEETING) {
          if (!res.botId) {
            throw Error("Could not start meeting: no bot found for meeting.");
          }
          try {
            await new BotApi(configuration).botStartRecording({
              botUuid: res.botId,
            });
          } catch (e) {
            // Handle bot creation errors specifically, because we want to show a different message
            // depending on whether the bot was a video call bot or a phone call bot.
            logError(
              "app/routes/notes.create.($id)/route.tsx bot creation error",
              e
            );
            return data({ botCreationFailed: true }, { status: 400 });
          }
        }

        if (action === ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING) {
          if (!res.botId) {
            throw Error("Could not end meeting: no bot found for meeting.");
          }
          await new BotApi(configuration).botLeaveMeeting({
            botUuid: res.botId,
          });
          return redirect(`/notes/`);
        }

        // Redirect with the note ID in the URL parameters, and as the final path entry.
        // The note ID from the URL parameters is used to match this create page with a note.
        // The note ID in the URL path is used to match this create page with an entry in the notes
        // list in the sidebar.
        const url = new URL(request.url);
        url.searchParams.set("noteID", res.noteId);
        url.pathname = `/notes/create/${res.noteId}`;

        // Remove information derived from the calendar event from the URL (if any).
        url.searchParams.delete("meetingTitle");
        url.searchParams.delete("meetingLink");
        url.searchParams.delete("attendees");
        url.searchParams.delete("linkedCRMEntityID");
        url.searchParams.delete("linkedCRMEntityName");
        url.searchParams.delete("startTime");
        url.searchParams.delete("endTime");
        url.searchParams.delete("scheduledEventUUID");
        return redirect(url.toString());
      case ActionTypes.DELETE_NOTE:
        if (!noteID) {
          throw Error("Delete note action requested without a note ID");
        }
        await noteAPI.noteDeleteNote({ noteId: noteID });
        return redirect("/notes");

      case ActionTypes.UPLOAD_AUDIO_CHUNK:
        if (!noteID) {
          throw Error("Upload audio chunk action requested without a note ID");
        }
        if (!createFormData.fileContent) {
          throw Error("Upload audio chunk action requested without a file");
        }
        if (createFormData.sequenceNumber === undefined) {
          throw Error(
            "Upload audio chunk action requested without a sequence number"
          );
        }
        if (createFormData.fragmentsNonce === undefined) {
          throw Error(
            "Upload audio chunk action requested without a fragment nonce"
          );
        }
        await noteAPI.noteUploadAudioChunk({
          audioData: createFormData.fileContent,
          noteId: noteID,
          sequence: createFormData.sequenceNumber,
          duration: fragmentDurationSeconds,
          mimeType: "audio/webm",
          nonce: createFormData.fragmentsNonce,
        });
        return data(
          { processedFragmentIndex: createFormData.sequenceNumber },
          { status: 200 }
        );
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  } catch (e) {
    logError("app/routes/notes.create.($id)/route.tsx action error", e);
    const error = e instanceof Error ? e.message : "Something went wrong";
    return data({ error }, { status: 400 });
  }
};

// Meeting types that match the legacy meeting types hard coded into this app.
const legacyMeetingTypes = [
  {
    uuid: "client",
    name: "Client",
    category: MeetingCategory.Client,
    isShared: true,
  },
  {
    uuid: "internal",
    name: "Internal",
    category: MeetingCategory.Internal,
    isShared: true,
  },
  {
    uuid: "debrief",
    name: "Debrief",
    category: MeetingCategory.Debrief,
    isShared: true,
  },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const attendeeOptions: AttendeeOptions = await getAttendeeOptions({
      request,
    });

    const { userId } = await getUserSessionOrRedirect(request);

    const searchParams = new URL(request.url).searchParams;

    // Load note details
    //
    // It's possible that the note data is both in the query params and the path. This component
    // uses the note in the query params as concerns the logic in this page that relies on a note.
    // The note in the query path is used by other components to match this create page with a note,
    // which may be a scheduled note that has been created before.
    const noteID = searchParams.get("noteID");
    const note = noteID
      ? await getNoteById({ noteId: noteID, request })
      : undefined;
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const bot = note?.botId
      ? await new BotApi(configuration).botGetBot({ botUuid: note.botId })
      : undefined;

    // Load interaction details
    const meetingArtifactsAPI = new MeetingArtifactsApi(configuration);
    const interactionId = note?.interactionUuid;
    const interactionPromise = interactionId
      ? meetingArtifactsAPI.meetingArtifactsGetClientInteraction({
          interactionUuid: interactionId,
        })
      : Promise.resolve(undefined);

    let eventSourceURL = undefined;
    if (bot && bot.platformId) {
      eventSourceURL = (
        await getEventStreamParams({ botID: bot.uuid, request })
      ).eventSourceURL;
    }

    const meetingTypesPromise = new MeetingArtifactsApi(
      configuration
    ).meetingArtifactsMeetingTypes();

    // Get attendees from the search parameters, if any.
    let prefilledAttendees: AttendeeOptions;
    try {
      const parsedAttendees = AttendeeOptionsStruct.safeParse(
        JSON.parse(safeAtob(searchParams.get("attendees") || ""))
      );
      prefilledAttendees = parsedAttendees.success ? parsedAttendees.data : [];
    } catch {
      prefilledAttendees = [];
    }

    const userAsAttendee = [
      attendeeOptions.find((item) => item.uuid === userId),
    ];

    const [interaction, meetingTypes] = await Promise.all([
      interactionPromise,
      meetingTypesPromise,
    ]);

    // Construct the lists of initial and selectable attendees.
    // If there is an existing note, use the attendees from the note as the initial attendees.
    // If not, use the attendees from the query params plus the user as the initial attendees.
    const {
      initialAttendees: initialAttendeesToUse,
      attendeeOptions: optionsToUse,
    } = resolvedAttendeeOptions(attendeeOptions, note?.attendees, [
      ...(note?.attendees === undefined ? userAsAttendee : []),
      ...prefilledAttendees,
    ]);

    return {
      note,
      bot,
      interaction,
      attendeeOptions: optionsToUse,
      initialAttendees: initialAttendeesToUse,
      userAsAttendee: userAsAttendee,
      userId,
      eventSourceURL,
      initialMeetingTitle:
        note?.meetingName || searchParams.get("meetingTitle") || undefined,
      initialMeetingLink:
        bot?.meetingLink || searchParams.get("meetingLink") || "",
      initialStartTime:
        note?.scheduledStartTime?.toISOString() ||
        searchParams.get("startTime") ||
        undefined,
      initialEndTime:
        note?.scheduledEndTime?.toISOString() ||
        searchParams.get("endTime") ||
        undefined,
      linkedCRMEntityID: searchParams.get("linkedCRMEntityID") || undefined,
      linkedCRMEntityName: searchParams.get("linkedCRMEntityName") || undefined,
      scheduledEventUUID: searchParams.get("scheduledEventUUID") || undefined,
      meetingTypes: meetingTypes,
    };
  } catch (e) {
    logError("auth.notes.create.($id) loader error", e);
    return {
      note: undefined,
      bot: undefined,
      interaction: undefined,
      attendeeOptions: [],
      initialAttendees: [],
      userId: "",
      eventSourceURL: undefined,
      initialMeetingTitle: undefined,
      initialMeetingLink: undefined,
      initialStartTime: undefined,
      initialEndTime: undefined,
      linkedCRMEntityID: undefined,
      linkedCRMEntityName: undefined,
      scheduledEventUUID: undefined,
      meetingTypes: {
        meetingTypes: [],
        defaultMeetingType: null,
      },
    };
  }
};

enum AudioSourceTypes {
  AudioUpload = "audio_upload",
  Mic = "mic",
  NoteTaker = "notetaker",
  PhoneCall = "phone_call",
}

const Route = () => {
  const {
    note,
    bot,
    interaction,
    attendeeOptions: initialAttendeeOptions,
    initialAttendees,
    userId,
    eventSourceURL,
    initialMeetingTitle,
    initialMeetingLink,
    initialStartTime,
    initialEndTime,
    linkedCRMEntityID,
    linkedCRMEntityName,
    scheduledEventUUID,
    meetingTypes,
  } = useLoaderData<{
    note: NoteResponse | undefined;
    bot: Bot | undefined;
    interaction: ClientInteraction | undefined;
    attendeeOptions: AttendeeOptions;
    initialAttendees: AttendeeOptions;
    userId: string;
    eventSourceURL: string | undefined;
    initialMeetingTitle: string | undefined;
    initialMeetingLink: string;
    initialStartTime: string | undefined;
    initialEndTime: string | undefined;
    linkedCRMEntityID: string | undefined;
    linkedCRMEntityName: string | undefined;
    scheduledEventUUID: string | undefined;
    meetingTypes: MeetingTypesResponse;
  }>();
  const params = useParams();
  const meetingSourceID = params.id !== note?.uuid ? params.id : undefined;
  const navigation = useNavigation();
  const navigate = useNavigate();
  const location = useLocation();
  const submit = useSubmit();
  const baseSaveNoteFetcher = useFetcher<{
    error?: string;
    botCreationFailed?: boolean;
  }>();
  const actionData = useActionData<{ error?: string }>();

  const showSuggestedMeetingLinks = useFlag("EnableMeetingLinkSuggestions");
  const enablePhoneCallRecordings = useFlag("EnablePhoneCallRecordings");
  const enableSaveScheduledNotes = useFlag("EnableSaveScheduledNotes") ?? false;
  const enableMeetingPrep = useFlag(
    "EnableClientIntelligencePreMeetingWorkflow"
  );
  const enableChunkedAudioUploads = useFlag("EnableChunkedAudioUploadsOnWeb");
  const enableAudioFileUploads = useFlag("EnableAudioFileUpload");

  const [searchParams] = useSearchParams();
  const tabFromSearchParams = searchParams.get("tab") ?? "record";
  const [meetingTab, setMeetingTab] = useState(
    enableMeetingPrep ? tabFromSearchParams : "record"
  );
  const updateMeetingTab = (meetingTab: string) => {
    setMeetingTab(meetingTab);
    searchParams.set("tab", meetingTab);
  };
  const [meetingLink, setMeetingLink] = useState<string>(
    bot?.meetingLink ?? initialMeetingLink
  );
  const botStatus = bot?.status;
  const [attendees, setAttendees] = useState<AttendeeOptions>(initialAttendees);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [touched, setTouched] = useState(false);
  const attendeePhoneNumbers = showSuggestedMeetingLinks
    ? attendees.flatMap((attendee) =>
        "phoneNumber" in attendee && attendee.phoneNumber
          ? [{ link: attendee.phoneNumber, name: attendee.name }]
          : []
      )
    : [];
  const resolvedMeetingTypes =
    meetingTypes.meetingTypes.length > 0
      ? meetingTypes.meetingTypes
      : legacyMeetingTypes;
  const [meetingType, setMeetingType] = useState<SerializeFrom<MeetingType>>(
    resolvedMeetingTypes.find((t) => note?.meetingTypeUuid === t.uuid) ??
      resolvedMeetingTypes.find(
        (t) => meetingTypes.defaultMeetingType === t.uuid
      ) ??
      resolvedMeetingTypes[0]!
  );
  const [title, setTitle] = useState<string>(
    initialMeetingTitle ||
      getMeetingNameFromAttendees(userId, attendees, meetingType)
  );
  const [showTitleError, setShowTitleError] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<Date>(
    initialStartTime ? new Date(initialStartTime) : new Date()
  );
  const [endTime, setEndTime] = useState<Date>(
    initialEndTime ? new Date(initialEndTime) : addHours(startTime, 1)
  );

  // Chunked audio upload

  // The nonce that is attached to fragments during a recording session. If the user resets the
  // audio, this will reset.
  const [fragmentsNonce, setFragmentsNonce] = useState(Date.now());

  // The fragments that need to be processed by the server.
  const [fragmentsToProcess, setFragmentsToProcess] = useState<
    Record<number, Blob>
  >({});

  const {
    isRecording,
    isPaused,
    recordingBlob,
    stopRecordingWithWakeLock,
    recordingFragments,
  } = useContext(RecorderContext);

  // A fetcher for handling audio fragments.
  //
  // We don't want fragment uploading to be a navigating operation, so a fetcher makes more sense.
  const audioBufferFetcher = useFetcher<{ processedFragmentIndex?: number }>();

  // The fetcher used for (some) note save operations.
  const micSaveNoteFetcher = useFetcher<{
    error?: String;
    draftNoteID?: string;
  }>();

  // The ID for the note to use for uploading audio fragment data.
  //
  // Bias towards the ID of the note as derived from the URL parameters; fall back to the draft note
  // ID created when starting recording if that's not available. We assume that the parameter-derived
  // note ID is more authoritative than the draft note ID; and, if we navigate from one create page to
  // another, the draft note ID may be stale.
  const fragmentNoteID = note?.uuid ?? micSaveNoteFetcher?.data?.draftNoteID;

  // Add fragments for processing as they are generated.
  useEffect(() => {
    if (!enableChunkedAudioUploads) {
      return;
    }

    const lastFragmentIndex = recordingFragments.length - 1;
    const lastFragment = recordingFragments[lastFragmentIndex];
    if (!lastFragment) {
      setFragmentsToProcess({});
      return;
    }
    setFragmentsToProcess((prev) => {
      return {
        ...prev,
        [lastFragmentIndex]: lastFragment,
      };
    });
  }, [enableChunkedAudioUploads, recordingFragments]);

  // Remove fragments from the list of fragments to process as they are processed.
  useEffect(() => {
    if (!enableChunkedAudioUploads) {
      return;
    }
    if (audioBufferFetcher.state !== "idle") {
      return;
    }
    const processedFragmentIndex =
      audioBufferFetcher.data?.processedFragmentIndex;
    if (processedFragmentIndex === undefined) {
      return;
    }
    setFragmentsToProcess((prev) => {
      const newFragmentsToProcess = { ...prev };
      delete newFragmentsToProcess[processedFragmentIndex];
      return newFragmentsToProcess;
    });
  }, [
    audioBufferFetcher.data?.processedFragmentIndex,
    audioBufferFetcher.state,
    enableChunkedAudioUploads,
  ]);

  // Process fragments as needed.
  useEffect(() => {
    if (!enableChunkedAudioUploads) {
      return;
    }

    // Upload one fragment. If this succeeds and there are multiple fragments to process, the next
    // fragment will be uploaded in the next render cycle.
    const [index, fragment] = Object.entries(fragmentsToProcess)[0] ?? [];
    if (!index || !fragment) {
      return;
    }

    if (!navigator.onLine) {
      // If the user is offline, don't upload the fragment. This will be retried later, when another
      // fragment is delivered.
      return;
    }

    const formData = new FormData();
    formData.append("action", ActionTypes.UPLOAD_AUDIO_CHUNK);
    formData.append("fileContent", fragment);
    formData.append("sequenceNumber", index.toString());
    formData.append("fragmentsNonce", fragmentsNonce.toString());
    const noteUUID = fragmentNoteID;
    audioBufferFetcher.submit(formData, {
      action: noteUUID
        ? `/notes/create/${noteUUID}?noteID=${noteUUID}`
        : "/notes/create",
      encType: "multipart/form-data",
      method: "post",
    });
    // Leave out the fetcher; we don't want this effect to run every time the fetcher's state changes.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    fragmentsToProcess,
    enableChunkedAudioUploads,
    fragmentsNonce,
    note?.uuid,
    fragmentNoteID,
  ]);

  const showPhoneCallTabForBot = enablePhoneCallRecordings
    ? isValidPhoneNumber(meetingLink, "US")
    : false;
  const [selectedAudioTab, setSelectedAudioTab] = useState<string>(
    meetingLink
      ? showPhoneCallTabForBot
        ? AudioSourceTypes.PhoneCall
        : AudioSourceTypes.NoteTaker
      : AudioSourceTypes.Mic
  );
  const toastId = useRef<Id | null>(null);
  const [spinnerCTA, setSpinnerCTA] = useState("");

  const { isTutorialEnabled, completeTutorial } = useOnboarding(
    "meeting-prep",
    { triggerViaUrl: true }
  );
  const introRef = useRef<Steps>(null);

  // in case onboarding experience is underway, show the "Use Notetaker" audio source
  useEffect(() => {
    if (isTutorialEnabled) {
      setSelectedAudioTab(AudioSourceTypes.NoteTaker);
    }
  }, [isTutorialEnabled]);

  const openDeleteModal = () => setIsDeleteModalOpen(true);
  const closeDeleteModal = () => setIsDeleteModalOpen(false);

  const confirmDelete = () => {
    handleDelete();
    closeDeleteModal();
  };

  const handleTitleBlur = () => {
    if (!isValidTitle(title)) {
      setShowTitleError(true);
    }
  };

  const handleTitleChange = (value: string) => {
    setShowTitleError(false);
    setTouched(true);
    setTitle(value);
  };

  // This isn't quite ideal–it would be nice to only set up the event source if the URL were truthy,
  // but that breaks React's requirement that hooks be called in the same order. The code here will
  // error when the URL is empty (and when the flag is disabled on the server), but the flow still
  // works.
  let streamedBotStatus = useEventSource(
    eventSourceURL || ""
  ) as BotStatus | null;
  const unifiedBotStatus = streamedBotStatus ?? botStatus;

  const botUUID = bot?.uuid ?? "";
  const isVideoMeeting = bot?.meetingType === BotMeetingType.VideoCall;
  // Show a toast if the bot is in the waiting room, or was not created.
  useEffect(() => {
    if (unifiedBotStatus == BotStatus.InWaitingRoom && isVideoMeeting) {
      toastId.current = toast.info("Check the waiting room!", {
        toastId: "bot-in-waiting-room",
      });
    }
  }, [unifiedBotStatus, botUUID, isVideoMeeting, enableSaveScheduledNotes]);

  // Update the title based on the attendees and meeting type, if there was no prefilled title
  // provided via the query params or the note title.
  useEffect(() => {
    if (initialMeetingTitle) {
      return;
    }
    setTitle(getMeetingNameFromAttendees(userId, attendees, meetingType));
  }, [initialMeetingTitle, userId, attendees, meetingType]);

  const saveNote = useCallback(
    (
      action: ActionTypes = ActionTypes.SAVE_NOTE,
      fileContent: Blob | null = null
    ) => {
      const formData = new FormData();
      // Use the old behavior if the feature for saving scheduled notes is not enabled.
      if (
        !enableSaveScheduledNotes &&
        (action == ActionTypes.SAVE_NOTE_AND_START_MEETING ||
          action == ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING)
      ) {
        action = ActionTypes.SAVE_NOTE;
      }
      formData.append("action", action);
      formData.append("meetingName", title);
      formData.append("meetingType", meetingType.uuid);
      formData.append("meetingLink", meetingLink);
      if (fileContent) {
        formData.append("fileContent", fileContent);
        formData.append("fileType", fileContent.type);
      }
      if (meetingSourceID) {
        formData.append("meetingSourceID", meetingSourceID);
      }
      if (enableSaveScheduledNotes && !scheduledEventUUID) {
        formData.append("startTime", startTime.toISOString());
        formData.append("endTime", endTime.toISOString());
      }
      if (scheduledEventUUID) {
        formData.append("scheduledEventUUID", scheduledEventUUID);
      }
      if (linkedCRMEntityID && linkedCRMEntityName) {
        formData.append("linkedCRMEntityID", linkedCRMEntityID);
        formData.append("linkedCRMEntityName", linkedCRMEntityName);
      }
      formData.append(
        "attendees",
        JSON.stringify(
          attendees.map((item) => ({
            name: item.name,
            type: item.type,
            uuid: item.uuid,
          }))
        )
      );

      setTouched(false);

      // Don't trigger a navigation if we are uploading audio data or creating a draft note. This is
      // a workaround for the fact that the hook that shows a dialog when the user tries to navigate
      // away from the page while there are unsaved changes is triggered before the updated value of
      // `touched` is propagated, and for the fact that the note created during the chunked upload flow
      // should not trigger a navigation.
      //
      // This is a hackish fix. We should find a better way to handle this.
      const noteUUID = note?.uuid ?? fragmentNoteID;
      const submitFunction =
        fileContent || action == ActionTypes.CREATE_DRAFT_NOTE
          ? micSaveNoteFetcher.submit
          : baseSaveNoteFetcher.submit;
      submitFunction(formData, {
        action: noteUUID
          ? `/notes/create/${noteUUID}?noteID=${noteUUID}`
          : "/notes/create",
        method: "post",
        encType: "multipart/form-data",
      });
    },
    [
      enableSaveScheduledNotes,
      title,
      meetingType.uuid,
      meetingLink,
      meetingSourceID,
      attendees,
      note?.uuid,
      fragmentNoteID,
      micSaveNoteFetcher.submit,
      baseSaveNoteFetcher.submit,
      startTime,
      endTime,
      linkedCRMEntityID,
      linkedCRMEntityName,
      scheduledEventUUID,
    ]
  );

  useEffect(() => {
    if (
      note?.status === ProcessingStatus.Finalized ||
      note?.status === ProcessingStatus.Processed
    ) {
      setTouched(false);
      return;
    }
    if (
      unifiedBotStatus == BotStatus.CallEnded ||
      unifiedBotStatus == BotStatus.RecordingDone
    ) {
      setTouched(false);
      saveNote(ActionTypes.SAVE_NOTE);
      navigate("/notes");
    }
  }, [note, navigate, unifiedBotStatus, saveNote, setTouched]);

  // Show a toast if there is an error posting the form data. The form is posted for several
  // operations, so there are several different errors that may be displayed here.
  useEffect(() => {
    if (toastId.current && toast.isActive(toastId.current)) {
      return;
    }

    // This isn't a perfect way to track the error: it's possible that the error is from a previous
    // fetch operation and hasn't yet been cleared. We should revisit this.
    const error =
      actionData?.error ??
      micSaveNoteFetcher.data?.error ??
      baseSaveNoteFetcher.data?.error;
    if (error) {
      toastId.current = toast.error(error, { toastId: "notes-create-error" });
      // If there was an error with the action, assume that something was changed that has not yet
      // been persisted. This isn't a perfectly-correct assumption, but it helps prevent the user
      // from losing data (at the expense of potentially an unnecessary warning dialog upon leaving
      // the page).
      setTouched(true);
      return;
    }

    // Handle bot creation errors specifically, because we want to show a different message for
    // video bots versus phone call bots.
    if (baseSaveNoteFetcher.data?.botCreationFailed ?? false) {
      toastId.current = toast.error(
        isVideoMeeting
          ? "Could not send Notetaker to your meeting. Please check the meeting link and try again."
          : "Could not connect or record the phone call. Please check the phone number and try again.",
        {
          toastId: "bot-not-created",
        }
      );
      return;
    }
  }, [
    actionData,
    baseSaveNoteFetcher.data?.botCreationFailed,
    baseSaveNoteFetcher.data?.error,
    isVideoMeeting,
    micSaveNoteFetcher.data?.error,
  ]);

  // `recordingBlob` becomes available once a recording is _fully stopped_. As
  // soon as it is available, trigger form submission via this hook.
  useEffect(() => {
    if (!recordingBlob || !navigator.onLine) {
      return;
    }
    saveNote(ActionTypes.SAVE_NOTE, recordingBlob);
  }, [recordingBlob, saveNote]);

  useBeforeUnload(
    useCallback(
      (event) => {
        if (!touched) {
          return;
        }
        event.preventDefault();
        event.returnValue = "Discard unsaved changes?";
      },
      [touched]
    )
  );

  unstable_usePrompt({
    message: "Discard unsaved changes?",
    when: ({ currentLocation, nextLocation }) => {
      return touched && currentLocation.pathname !== nextLocation.pathname;
    },
  });

  const handleDelete = () => {
    const formData = new FormData();
    formData.append("action", ActionTypes.DELETE_NOTE);
    submit(formData, {
      action: note?.uuid
        ? `/notes/create/${note.uuid}?noteID=${note.uuid}`
        : "/notes/create",
      method: "post",
      encType: "multipart/form-data",
    });
  };

  const saveDisabledBotStatuses = new Set<BotStatus>([
    BotStatus.InWaitingRoom,
    BotStatus.InCallNotRecording,
    BotStatus.InCallRecording,
  ]);

  // When there is a bot, check if it is in a state that should cause the save button to be
  // disabled. We don't want users to be able to save bot meetings, because it causes confusion.
  const shouldDisableForBotMeeting =
    unifiedBotStatus && saveDisabledBotStatuses.has(unifiedBotStatus);

  // When there is mic audio, the meeting should always be saveable, and saving should save the mic
  // audio.
  const shouldEnableForMicMeeting =
    selectedAudioTab === AudioSourceTypes.Mic && (isRecording || recordingBlob);

  // The save button should be enabled when:
  // - the title is valid
  // - the form is not submitting
  // - if this is a bot meeting, the bot is not in a state where saving is disabled
  // - if this is a mic meeting, the recording is ongoing; or, the feature to allow creating
  //   scheduled notes is enabled.
  //
  // In the case of a meeting with both bot and mic audio, the save button should track the state of
  // the mic audio; we assume that, if the user has started a mic session after a bot, they want the
  // mic audio. We can more easily recover lost bot audio than mic audio that was never uploaded.
  const enableSaveNoteButton =
    isValidTitle(title) &&
    navigation.state === "idle" &&
    micSaveNoteFetcher.state === "idle" &&
    baseSaveNoteFetcher.state === "idle" &&
    !shouldDisableForBotMeeting &&
    (shouldEnableForMicMeeting || enableSaveScheduledNotes);

  const isLoading =
    navigation.state !== "idle" ||
    micSaveNoteFetcher.state !== "idle" ||
    baseSaveNoteFetcher.state !== "idle";

  const onCompleteTutorial = () => {
    completeTutorial();
    navigate("/notes", { replace: true });
  };

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteTutorial();
    }
  };

  // handle dynamic elements - check if element exists and make IntroJS get the updated element
  const handleBeforeChange = async (nextStepIndex: number) => {
    const { hasDynamicElement, element } = tutorialSteps[nextStepIndex] || {};
    if (hasDynamicElement && element) {
      const targetElement = document.querySelector(element);
      if (targetElement) {
        introRef.current?.updateStepElement(nextStepIndex);
      }
    }
  };

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          className="px-0 md:px-5"
          title="Create a note"
          subtitle="Create a note"
          left={
            <BackButton to={location.state?.from ?? "/notes"} tooltip="Back" />
          }
          right={
            <div className="flex flex-row gap-2">
              {note && (
                <DeleteButton onClick={openDeleteModal} tooltip="Delete Note" />
              )}
              <SaveButton
                disabled={!enableSaveNoteButton}
                loading={isLoading}
                tooltip={
                  unifiedBotStatus && shouldDisableForBotMeeting
                    ? "The note will save automatically after the meeting ends."
                    : enableSaveScheduledNotes && isRecording
                      ? "End meeting"
                      : "Save"
                }
                title={
                  enableSaveScheduledNotes && isRecording
                    ? "End meeting"
                    : "Save"
                }
                onClick={() => {
                  const isBotMeeting =
                    selectedAudioTab === AudioSourceTypes.NoteTaker ||
                    selectedAudioTab === AudioSourceTypes.PhoneCall;

                  // If the user is offline, warn them that the operation cannot be completed.
                  if (!navigator.onLine) {
                    toast.error(
                      "You are offline. Please check your connection."
                    );
                    return;
                  }

                  if (enableSaveScheduledNotes && !isRecording) {
                    saveNote(ActionTypes.SAVE_NOTE);
                    return;
                  }

                  // If this is a bot meeting, then save the note accordingly.
                  if (isBotMeeting) {
                    saveNote(ActionTypes.SAVE_NOTE);
                  } else {
                    // The recorder library is weird. It only gives access to the recording blob
                    // after recording has been stopped. This means that stopping recording is
                    // equivalent to saving the note. It's all weirdly inverted, so we have to do
                    // some gymnastics. First, we stop recording here. That causes `recordingBlob`
                    // to become available via RecorderContext. We then finally submit the form via
                    // a `useEffect`.
                    stopRecordingWithWakeLock();
                  }
                }}
              />
            </div>
          }
        />
      }
    >
      <Steps
        enabled={isTutorialEnabled}
        steps={tutorialSteps}
        initialStep={0}
        onExit={onExitTutorial}
        onComplete={onCompleteTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
        onBeforeChange={handleBeforeChange}
        ref={introRef}
      />

      <div className="flex flex-col gap-3 self-stretch md:px-5">
        <div className="flex w-full flex-col py-2">
          <input
            value={title}
            autoFocus
            onBlur={handleTitleBlur}
            onChange={(event) => handleTitleChange(event.currentTarget.value)}
            placeholder="Add a note title"
            className={cn(
              "w-full border-none text-3xl font-semibold focus:outline-none",
              showTitleError ? "text-red-600" : "text-black"
            )}
          />
          {showTitleError && (
            <Typography variant="body2" color="error">
              A note title is required
            </Typography>
          )}
        </div>
        <AfterHydration>
          <Typography className="inline-flex items-center rounded-md">
            <Clock />
            <span className="ml-1 mr-2">Created</span>
            <Typography asChild color="secondary" variant="body2">
              <span>
                {format(
                  note?.created ? new Date(note.created) : new Date(),
                  "ccc, MMM do, h:mm aaa"
                )}
              </span>
            </Typography>
          </Typography>
          {enableSaveScheduledNotes ? (
            <>
              <Typography
                className="inline-flex items-center rounded-md"
                data-onboarding="scheduled-timestamp"
              >
                <Calendar />
                <span className="ml-1 mr-2">Start time</span>
                <span>
                  <input
                    className={"border-1 rounded-md border border-border p-1"}
                    aria-label="Meeting start date and time"
                    type="datetime-local"
                    min={format(new Date(), "yyyy-MM-dd'T'HH:mm")}
                    value={format(startTime, "yyyy-MM-dd'T'HH:mm")}
                    onChange={(e) => {
                      if (!e.target.validity.valid) {
                        return;
                      }
                      if (!e.target.value) {
                        return;
                      }
                      const localDate = new Date(e.target.value);
                      if (localDate > endTime) {
                        setEndTime(localDate);
                      }
                      setStartTime(localDate);
                      setTouched(true);
                    }}
                  />
                </span>
              </Typography>
              <Typography className="inline-flex items-center rounded-md">
                <Calendar />
                <span className="ml-1 mr-2">End time</span>
                <span>
                  <input
                    className={"border-1 rounded-md border border-border p-1"}
                    aria-label="Meeting end date and time"
                    type="datetime-local"
                    min={format(new Date(), "yyyy-MM-dd'T'HH:mm")}
                    value={format(endTime, "yyyy-MM-dd'T'HH:mm")}
                    onChange={(e) => {
                      if (!e.target.validity.valid) {
                        return;
                      }
                      if (!e.target.value) {
                        return;
                      }
                      const localDate = new Date(e.target.value);
                      if (localDate < startTime) {
                        setStartTime(localDate);
                      }
                      setEndTime(localDate);
                      setTouched(true);
                    }}
                  />
                </span>
              </Typography>
            </>
          ) : initialStartTime || initialEndTime ? (
            <>
              {initialStartTime && (
                <Typography className="inline-flex items-center rounded-md">
                  <Calendar />
                  <span className="mx-1 grow">Start time</span>
                  <Typography asChild color="secondary" variant="body2">
                    <span>
                      {format(
                        new Date(initialStartTime),
                        "ccc, MMM do, h:mm aaa"
                      )}
                    </span>
                  </Typography>
                </Typography>
              )}
              {initialEndTime && (
                <Typography className="inline-flex items-center rounded-md">
                  <Calendar />
                  <span className="mx-1 grow">End time</span>
                  <Typography asChild color="secondary" variant="body2">
                    <span>
                      {format(
                        new Date(initialEndTime),
                        "ccc, MMM do, h:mm aaa"
                      )}
                    </span>
                  </Typography>
                </Typography>
              )}
            </>
          ) : null}
        </AfterHydration>

        <Separator className="my-2" />

        {enableMeetingPrep && (
          <ToggleGroup
            className="scollbar-hidden w-full justify-start overflow-auto p-0"
            data-onboarding="meeting-tabs"
            value={meetingTab}
            type="single"
            onValueChange={(value) => {
              if (!value) {
                return;
              }
              updateMeetingTab(value);
            }}
          >
            <ToggleGroupItem value="record" aria-label="Show Meeting Details">
              Meeting Details
            </ToggleGroupItem>
            <ToggleGroupItem
              value="prep"
              aria-label="Show Meeting Prep"
              // Don't allow the user to generate prep if they have mic audio.
              //
              // This is a quick fix for https://zeplyn.atlassian.net/browse/ENG-1089.
              disabled={!interaction && isRecording}
            >
              Meeting Prep
            </ToggleGroupItem>
          </ToggleGroup>
        )}

        <div
          className={cn(
            "flex flex-col gap-2",
            enableMeetingPrep && meetingTab === "prep" && "hidden"
          )}
        >
          <MeetingTypeSelector
            meetingType={meetingType}
            setMeetingType={(type) => {
              setTouched(true);
              setMeetingType(type);
            }}
            meetingTypes={resolvedMeetingTypes}
          />
          <FormField id="attendees" name="attendees">
            <FormLabel>
              {meetingType.category === "debrief" ? "Clients" : "Attendees"}
            </FormLabel>
            <FormControl>
              <AttendeesMultiSelect
                placeholder={
                  meetingType.category === "debrief"
                    ? "List clients"
                    : "List attendees"
                }
                emptyLabel="Start typing to add new attendee."
                leftIcon={<Users />}
                initialOptions={initialAttendeeOptions}
                selected={attendees}
                onChange={(nextAttendees) => {
                  setAttendees(nextAttendees);
                  setTouched(true);
                }}
                triggerClassName="w-full"
                commandClassName="w-full"
              />
            </FormControl>
            <FormDescription className="flex justify-between">
              Listing other attendees and clients is highly recommended
            </FormDescription>
          </FormField>
          <div data-onboarding="audio-source">
            <TooltipProvider>
              <Typography variant="body3">Audio Source</Typography>
              <Tabs
                value={selectedAudioTab}
                className="space-y-4"
                onValueChange={setSelectedAudioTab}
              >
                <TabsList>
                  {/* TODO: @debojyotighosh Nested buttons error in /notes/create/$id page */}
                  <Tooltip>
                    <TooltipTrigger>
                      <TabsTrigger
                        value={AudioSourceTypes.Mic}
                        disabled={isRecording || isPaused}
                      >
                        Use Mic
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Use your microphone for note-taking. If using this
                        option for video meetings, you shouldn't wear ear phones
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger>
                      <TabsTrigger
                        value={AudioSourceTypes.NoteTaker}
                        disabled={isRecording || isPaused}
                      >
                        Use Notetaker
                      </TabsTrigger>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Send the notetaker to your video meeting to take notes
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  {enablePhoneCallRecordings && (
                    <Tooltip>
                      <TooltipTrigger>
                        <TabsTrigger
                          value={AudioSourceTypes.PhoneCall}
                          disabled={isRecording || isPaused}
                        >
                          Call a phone
                        </TabsTrigger>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Record a phone call to take notes</p>
                      </TooltipContent>
                      {enableAudioFileUploads && (
                        <TooltipTrigger>
                          <TabsTrigger
                            value={AudioSourceTypes.AudioUpload}
                            disabled={isRecording || isPaused}
                          >
                            Upload Recording
                          </TabsTrigger>
                        </TooltipTrigger>
                      )}
                    </Tooltip>
                  )}
                </TabsList>
              </Tabs>
            </TooltipProvider>
          </div>

          {enableAudioFileUploads &&
            selectedAudioTab === AudioSourceTypes.AudioUpload && (
              <AudioFileUploader
                needConsent={meetingType.category === "client"}
                setTouched={setTouched}
                onFileUploaded={(file) => {
                  saveNote(ActionTypes.SAVE_NOTE, file);
                }}
              />
            )}
          {selectedAudioTab === AudioSourceTypes.Mic && (
            <RecorderCard
              onStartRecording={() => {
                setTouched(true);
                setFragmentsNonce(Date.now());
                if (!enableChunkedAudioUploads) {
                  return;
                }
                if (note?.uuid ?? fragmentNoteID) {
                  return;
                }
                saveNote(ActionTypes.CREATE_DRAFT_NOTE);
                // Ensure that the note remains in a dirty state so that page refreshes and
                // navigations don't lose mic audio data.
                setTouched(true);
              }}
              needConsent={meetingType.category === "client"}
              fragmentMilliseconds={
                enableChunkedAudioUploads
                  ? fragmentDurationSeconds * 1000
                  : undefined
              }
            />
          )}
          {selectedAudioTab === AudioSourceTypes.NoteTaker && (
            <NoteTakerController
              botId={note?.botId ?? ""}
              botStatus={unifiedBotStatus}
              meetingLink={meetingLink}
              setMeetingLink={setMeetingLink}
              meetingLinkSuggestions={[]}
              onTouched={() => setTouched(true)}
              needConsent={
                !isTutorialEnabled && meetingType.category === "client"
              }
              sendBotAndCreateNote={() =>
                saveNote(ActionTypes.SAVE_NOTE_AND_START_MEETING)
              }
              saveNoteTaking={() =>
                saveNote(ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING)
              }
              spinnerCTA={spinnerCTA}
              setSpinnerCTA={setSpinnerCTA}
              supportsPauseResume={bot?.supportsPauseResume ?? true}
              botType={BotMeetingType.VideoCall}
              botOperationLoading={isLoading}
            />
          )}
          {enablePhoneCallRecordings &&
            selectedAudioTab === AudioSourceTypes.PhoneCall && (
              <NoteTakerController
                botId={note?.botId ?? ""}
                botStatus={unifiedBotStatus}
                meetingLink={meetingLink}
                meetingLinkSuggestions={attendeePhoneNumbers}
                setMeetingLink={setMeetingLink}
                onTouched={() => setTouched(true)}
                needConsent={meetingType.category === "client"}
                sendBotAndCreateNote={() =>
                  saveNote(ActionTypes.SAVE_NOTE_AND_START_MEETING)
                }
                saveNoteTaking={() =>
                  saveNote(ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING)
                }
                spinnerCTA={spinnerCTA}
                setSpinnerCTA={setSpinnerCTA}
                supportsPauseResume={bot?.supportsPauseResume ?? true}
                botType={BotMeetingType.PhoneCall}
                botOperationLoading={isLoading}
              />
            )}
        </div>
        {enableMeetingPrep && (
          <div
            className={cn(
              "flex flex-col gap-2",
              meetingTab === "prep" ? null : "hidden"
            )}
          >
            <MeetingPrepTab
              interaction={interaction}
              clients={attendees.filter((a) => a.type === "client")}
              readOnly={false}
            />
            <MeetingTypeModal
              isOpen={meetingTab == "prep" && !interaction}
              onClose={() => updateMeetingTab("record")}
              meetingTypes={resolvedMeetingTypes}
              meetingType={meetingType}
              setMeetingType={setMeetingType}
              onSave={() => {
                saveNote(ActionTypes.SAVE_NOTE);
              }}
              isLoading={isLoading}
            />
          </div>
        )}
      </div>
      <ConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={confirmDelete}
        title="Confirm Delete"
        message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
      />
    </SidebarV2>
  );
};

export default () => (
  <RecorderProvider>
    <Route />
  </RecorderProvider>
);
