import { Link, useLocation } from "react-router";
import { EmailNoteButton } from "~/routes/_auth.notes.$id._index/EmailNoteButton";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { EllipsisVertical, Lock } from "lucide-react";
import { WealthboxIcon } from "~/@ui/assets/WealthboxIcon";
import { RedtailLogo } from "~/@ui/assets/RedtailLogo";
import { SalesforceIcon } from "~/@ui/assets/SalesforceIcon";
import { SyncToCrmButton } from "~/routes/_auth.notes.$id._index/SyncToCrmButton";
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import {
  ApiRoutersNoteModelsClient,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { FollowUpEmailButton } from "~/routes/_auth.notes.$id._index/FollowUpEmail";
import { DividerVerticalIcon, TrashIcon } from "@radix-ui/react-icons";
import { Badge } from "~/@shadcn/ui/badge";
import { AttendeesList } from "./AttendeesList";
import { ReactNode } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "~/@shadcn/ui/popover";
import { MeetingTime } from "./ScheduledCard";
import SyncToCrmFlow from "~/routes/_auth.notes.$id._index/components/SyncToCrm/SyncToCrmFlow";
import { useFlag } from "~/context/flags";

// Displays a button that maps to the most relevant next action that can be taken on a note.
const NextBestActionButton = ({
  noteId,
  status,
  isEmpty,
  hasClient,
  crmSystem,
  syncToCRM,
  onDelete,
  fromLink,
}: {
  noteId: string;
  status: ProcessingStatus;
  isEmpty: boolean;
  hasClient: boolean;
  crmSystem: string | null | undefined;
  syncToCRM: () => void;
  onDelete: () => void;
  fromLink: string;
}) => {
  const NotesLinkButton = ({
    variant = "default",
    children,
  }: {
    variant?: "outline_primary" | "default";
    children: ReactNode;
  }) => (
    <Button variant={variant} asChild>
      <Link to={`/notes/${noteId}`} state={{ from: fromLink }}>
        {children}
      </Link>
    </Button>
  );

  const isCrmSyncRevampEnabled = useFlag("EnableCRMSyncSelection");

  if (status === ProcessingStatus.Uploaded) {
    return (
      <NotesLinkButton variant="outline_primary">Processing</NotesLinkButton>
    );
  }

  if (isEmpty || status === ProcessingStatus.Scheduled) {
    return (
      <Button variant="destructive" onClick={onDelete}>
        <TrashIcon />
        Delete
      </Button>
    );
  }

  if (status === ProcessingStatus.Processed) {
    if (hasClient) {
      return isCrmSyncRevampEnabled ? (
        <SyncToCrmFlow noteId={noteId} />
      ) : (
        <SyncToCrmButton
          noteId={noteId}
          disabled={false}
          size="default"
          variant="default"
          title="Sync to CRM"
          handleSave={syncToCRM}
        />
      );
    } else {
      return <NotesLinkButton>Review note</NotesLinkButton>;
    }
  }

  if (status === ProcessingStatus.Finalized) {
    const FinalizedIcon = iconForFinalizedState(crmSystem);
    return FinalizedIcon ? (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger className="cursor-default">
            <FinalizedIcon
              className="m-2 h-6 w-6"
              aria-label={`Note has been finalized and synced to ${crmSystem}`}
            />
          </TooltipTrigger>
          <TooltipContent>Note has been finalized</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    ) : null;
  }

  return <NotesLinkButton>Review note</NotesLinkButton>;
};

// Returns the icon that should be displayed for a note that has been finalized.
//
// This is based on the CRM system that the note was synced to.
const iconForFinalizedState = (crmSystem: string | null | undefined) => {
  if (!crmSystem) return Lock;
  if (crmSystem === "wealthbox") return WealthboxIcon;
  if (crmSystem === "redtail") return RedtailLogo;
  if (crmSystem.includes("saleforce") || crmSystem === "sequoia_sf") {
    return SalesforceIcon;
  }
  return null;
};

// Displays a hub card for a completed note.
//
// This card allows the user to take a few important actions on the note, access the note, and start
// working on the "next best action" (which may happen inline or which may require opening up the
// note details page.
interface PastNoteCardProps {
  noteId: string;
  meetingName: string;
  meetingType?: string | null;
  date: Date;
  client: ApiRoutersNoteModelsClient | null;
  status: ProcessingStatus;
  isEmpty: boolean;
  crmSystem?: string | null;
  syncToCRM: () => void;
  onDelete: () => void;
  to: { pathname: string };
  useDesktopLayout: boolean;
}

const StatusRelatedItems = ({
  noteId,
  status,
  isEmpty,
}: {
  noteId: string;
  status: ProcessingStatus;
  isEmpty: boolean;
}) =>
  isEmpty ? (
    <Badge variant="secondary">Empty note</Badge>
  ) : status === ProcessingStatus.Scheduled ? (
    <Badge variant="secondary">No notes</Badge>
  ) : status === ProcessingStatus.Finalized ||
    status === ProcessingStatus.Processed ? (
    <>
      <EmailNoteButton noteId={noteId} />
      <FollowUpEmailButton noteId={noteId} fetchOnMount={false} />
    </>
  ) : null;

const PastNoteCard = ({
  noteId,
  meetingName,
  meetingType,
  date,
  client,
  status,
  isEmpty,
  crmSystem,
  syncToCRM,
  onDelete,
  to,
  useDesktopLayout,
}: PastNoteCardProps) => {
  const location = useLocation();
  const fromLink = `${location.pathname}${location.search}`;
  return (
    <div className="flex w-full flex-row items-center justify-between gap-2 rounded-2xl border px-2 py-1 sm:px-4 sm:py-2">
      <div className="flex w-full min-w-0 flex-col items-start gap-2 break-words sm:w-auto">
        <Link
          className="text-md w-full min-w-0 font-semibold text-[#182349] sm:w-auto sm:text-xl sm:underline"
          to={to}
          state={{ from: fromLink }}
        >
          {meetingName}
          {!useDesktopLayout && <MeetingTime startTime={date} />}
        </Link>
        {useDesktopLayout && (
          <>
            {client && client.name && (
              <AttendeesList
                attendees={[
                  { name: client.name, uuid: client.uuid, type: "client" },
                ]}
                className="mx-1"
              />
            )}
            <div className="flex items-center gap-2">
              <MeetingTime startTime={date} />
              <DividerVerticalIcon />
              <span className="text-base text-[#213CA1]">
                {meetingType || ""}
              </span>
            </div>
          </>
        )}
      </div>
      <div className="flex shrink-0 flex-row items-center justify-around gap-2">
        {useDesktopLayout ? (
          <StatusRelatedItems
            noteId={noteId}
            status={status}
            isEmpty={isEmpty}
          />
        ) : (
          status !== ProcessingStatus.Uploaded && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon">
                  <EllipsisVertical />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto"
                // Prevents auto focusing the first button in the popover (which triggers its
                // tooltip)
                onOpenAutoFocus={(event) => {
                  event.preventDefault();
                }}
              >
                <StatusRelatedItems
                  noteId={noteId}
                  status={status}
                  isEmpty={isEmpty}
                />
              </PopoverContent>
            </Popover>
          )
        )}
        <div className="py-2">
          <NextBestActionButton
            noteId={noteId}
            status={status}
            isEmpty={isEmpty}
            hasClient={client !== null}
            crmSystem={crmSystem}
            syncToCRM={syncToCRM}
            onDelete={onDelete}
            fromLink={fromLink}
          />
        </div>
      </div>
    </div>
  );
};

export default PastNoteCard;
