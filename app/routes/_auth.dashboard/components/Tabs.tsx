import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";

const MeetingTabs = ({
  value,
  onChange,
  useDesktopLayout,
}: {
  value: string;
  onChange: (value: string) => void;
  useDesktopLayout: boolean;
}) => {
  return (
    <Tabs
      value={value}
      onValueChange={onChange}
      className="self-start"
      data-onboarding="meeting-tabs"
    >
      <TabsList>
        {tabs(useDesktopLayout).map(({ label, value }) => (
          <TabsTrigger key={value} value={value}>
            {label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
};

const tabs = (isDesktop: boolean) => [
  {
    label: isDesktop ? "Today's Meetings" : "Today",
    value: "today",
  },
  {
    label: isDesktop ? "Upcoming Meetings" : "Upcoming",
    value: "upcoming",
  },
  {
    label: isDesktop ? "Past Meetings" : "Past",
    value: "past",
  },
];

export default MeetingTabs;
