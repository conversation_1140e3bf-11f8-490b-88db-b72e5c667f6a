// API URL: /feapi/notes/sync-to-crm
import { ActionFunctionArgs } from "react-router";
import { uploadNoteToCrm } from "~/api/notes/uploadToCrm.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const body = await request.json();
  const { noteId, uploadTargetId, syncItems } = body;

  const response = await uploadNoteToCrm({
    noteId,
    uploadTargetID: uploadTargetId,
    requestBody: syncItems,
    request,
  });

  return response;
};
