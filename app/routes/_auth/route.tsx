import { type LoaderFunctionArgs } from "react-router";
import { Outlet, useLoaderData } from "react-router";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { IntercomProvider } from "~/third-party/Intercom/Intercom";
import { fetchFlags } from "~/api/flags/flagFetcher.server";
import { FlagProvider } from "~/context/flags";
import { datadogRum } from "@datadog/browser-rum";
import { useEffect } from "react";
import { datadogLogs } from "@datadog/browser-logs";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // On page load, check if user is authenticated, and redirect to /auth/login
  // if they are not
  const userSession = await getUserSessionOrRedirect(request);
  const flags = await fetchFlags(request);
  const appId = process.env.ZEPLYN_INTERCOM_WIDGET_ID;
  if (!appId) throw Error("Missing Intercom app ID");

  return { appId, userSession, flags };
};

const Route = () => {
  const { appId, userSession, flags } = useLoaderData<typeof loader>();
  useEffect(() => {
    const user = {
      id: userSession.userId,
      organization_id: userSession.orgId,
    };
    datadogRum.setUser(user);
    datadogLogs.setUser(user);
  }, [userSession.userId, userSession.orgId]);

  return (
    <IntercomProvider
      appId={appId}
      email={userSession.email}
      firstName={userSession.firstName}
      lastName={userSession.lastName}
      userId={userSession.userId}
    >
      <FlagProvider flags={flags}>
        <Outlet />
      </FlagProvider>
    </IntercomProvider>
  );
};
export default Route;
