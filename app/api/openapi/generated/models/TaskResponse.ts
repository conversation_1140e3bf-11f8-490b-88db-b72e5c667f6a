/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersTaskModelsClient } from './ApiRoutersTaskModelsClient';
import {
    ApiRoutersTaskModelsClientFromJSON,
    ApiRoutersTaskModelsClientFromJSONTyped,
    ApiRoutersTaskModelsClientToJSON,
} from './ApiRoutersTaskModelsClient';

/**
 * 
 * @export
 * @interface TaskResponse
 */
export interface TaskResponse {
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    title: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    description?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof TaskResponse
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof TaskResponse
     */
    created: Date;
    /**
     * 
     * @type {Date}
     * @memberof TaskResponse
     */
    modified: Date;
    /**
     * 
     * @type {boolean}
     * @memberof TaskResponse
     */
    completed: boolean;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    parentNoteUuid: string | null;
    /**
     * 
     * @type {ApiRoutersTaskModelsClient}
     * @memberof TaskResponse
     */
    owner: ApiRoutersTaskModelsClient | null;
    /**
     * 
     * @type {ApiRoutersTaskModelsClient}
     * @memberof TaskResponse
     */
    assignee: ApiRoutersTaskModelsClient | null;
    /**
     * 
     * @type {Array<ApiRoutersTaskModelsClient>}
     * @memberof TaskResponse
     */
    assignees?: Array<ApiRoutersTaskModelsClient> | null;
}

/**
 * Check if a given object implements the TaskResponse interface.
 */
export function instanceOfTaskResponse(value: object): value is TaskResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    if (!('completed' in value) || value['completed'] === undefined) return false;
    if (!('parentNoteUuid' in value) || value['parentNoteUuid'] === undefined) return false;
    if (!('owner' in value) || value['owner'] === undefined) return false;
    if (!('assignee' in value) || value['assignee'] === undefined) return false;
    return true;
}

export function TaskResponseFromJSON(json: any): TaskResponse {
    return TaskResponseFromJSONTyped(json, false);
}

export function TaskResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TaskResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'title': json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'created': (new Date(json['created'])),
        'modified': (new Date(json['modified'])),
        'completed': json['completed'],
        'parentNoteUuid': json['parent_note_uuid'],
        'owner': ApiRoutersTaskModelsClientFromJSON(json['owner']),
        'assignee': ApiRoutersTaskModelsClientFromJSON(json['assignee']),
        'assignees': json['assignees'] == null ? undefined : ((json['assignees'] as Array<any>).map(ApiRoutersTaskModelsClientFromJSON)),
    };
}

export function TaskResponseToJSON(value?: TaskResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'title': value['title'],
        'description': value['description'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'created': ((value['created']).toISOString()),
        'modified': ((value['modified']).toISOString()),
        'completed': value['completed'],
        'parent_note_uuid': value['parentNoteUuid'],
        'owner': ApiRoutersTaskModelsClientToJSON(value['owner']),
        'assignee': ApiRoutersTaskModelsClientToJSON(value['assignee']),
        'assignees': value['assignees'] == null ? undefined : ((value['assignees'] as Array<any>).map(ApiRoutersTaskModelsClientToJSON)),
    };
}

