/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CRMSyncItemSelection } from './CRMSyncItemSelection';
import {
    CRMSyncItemSelectionFromJSON,
    CRMSyncItemSelectionFromJSONTyped,
    CRMSyncItemSelectionToJSON,
} from './CRMSyncItemSelection';

/**
 * 
 * @export
 * @interface CrmUploadNoteToCrmRequestValue
 */
export interface CrmUploadNoteToCrmRequestValue {
    /**
     * 
     * @type {boolean}
     * @memberof CrmUploadNoteToCrmRequestValue
     */
    includeSection?: boolean;
    /**
     * 
     * @type {Array<string>}
     * @memberof CrmUploadNoteToCrmRequestValue
     */
    includedItems?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof CrmUploadNoteToCrmRequestValue
     */
    excludedItems?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof CrmUploadNoteToCrmRequestValue
     */
    customTemplate?: string;
}

/**
 * Check if a given object implements the CrmUploadNoteToCrmRequestValue interface.
 */
export function instanceOfCrmUploadNoteToCrmRequestValue(value: object): value is CrmUploadNoteToCrmRequestValue {
    return true;
}

export function CrmUploadNoteToCrmRequestValueFromJSON(json: any): CrmUploadNoteToCrmRequestValue {
    return CrmUploadNoteToCrmRequestValueFromJSONTyped(json, false);
}

export function CrmUploadNoteToCrmRequestValueFromJSONTyped(json: any, ignoreDiscriminator: boolean): CrmUploadNoteToCrmRequestValue {
    if (json == null) {
        return json;
    }
    return {
        
        'includeSection': json['include_section'] == null ? undefined : json['include_section'],
        'includedItems': json['included_items'] == null ? undefined : json['included_items'],
        'excludedItems': json['excluded_items'] == null ? undefined : json['excluded_items'],
        'customTemplate': json['custom_template'] == null ? undefined : json['custom_template'],
    };
}

export function CrmUploadNoteToCrmRequestValueToJSON(value?: CrmUploadNoteToCrmRequestValue | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'include_section': value['includeSection'],
        'included_items': value['includedItems'],
        'excluded_items': value['excludedItems'],
        'custom_template': value['customTemplate'],
    };
}

