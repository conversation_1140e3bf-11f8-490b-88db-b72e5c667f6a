/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PreviewData
 */
export interface PreviewData {
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof PreviewData
     */
    previewData: { [key: string]: any; };
}

/**
 * Check if a given object implements the PreviewData interface.
 */
export function instanceOfPreviewData(value: object): value is PreviewData {
    if (!('previewData' in value) || value['previewData'] === undefined) return false;
    return true;
}

export function PreviewDataFromJSON(json: any): PreviewData {
    return PreviewDataFromJSONTyped(json, false);
}

export function PreviewDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): PreviewData {
    if (json == null) {
        return json;
    }
    return {
        
        'previewData': json['preview_data'],
    };
}

export function PreviewDataToJSON(value?: PreviewData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'preview_data': value['previewData'],
    };
}

