import { createContext, ReactNode, useContext } from "react";

type UiSettings = {
  theme: "light" | "dark";
  navbar: boolean;
};

const UiSettingsContext = createContext<UiSettings>({
  theme: "light",
  navbar: false,
});

type Props = { uiSettings: UiSettings; children: ReactNode };
const UiSettingsProvider = ({ uiSettings, children }: Props) => (
  <UiSettingsContext.Provider value={uiSettings}>
    {children}
  </UiSettingsContext.Provider>
);

function useUiSettings(name: string) {
  return useContext(UiSettingsContext);
}

export { UiSettingsContext, UiSettingsProvider, useUiSettings };
